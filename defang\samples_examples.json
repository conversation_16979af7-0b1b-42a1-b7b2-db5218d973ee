[{"projectName": "nodejs-openai", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    environment:\n      - OPENAI_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-openai project with Defang."}, {"projectName": "imgproxy", "compose": "services:\n  imgproxy:\n    restart: unless-stopped\n    image: darthsim/imgproxy\n    ports:\n      - mode: ingress\n        target: 8080\n        published: 8080\n        protocol: tcp\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n", "dockerfile": "", "technologies": [], "description": "A  application that demonstrates how to deploy a imgproxy project with Defang."}, {"projectName": "flask", "compose": "services:\n  flask:\n    restart: unless-stopped\n    build:\n      context: ./flask\n      dockerfile: Dockerfile\n    deploy:\n      resources:\n        reservations:\n          cpus: \"1.0\"\n          memory: 512M\n    ports:\n      - mode: ingress\n        target: 5000\n        published: 5000\n    healthcheck:\n      test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request; urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a base image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required packages\nRUN apt-get update -qq \\\n      && apt-get install -y --no-install-recommends \\\n      build-essential \\\n      python3-dev \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run app.py when the container launches\nCMD [\"python\", \"app.py\"]\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a flask project with Defang."}, {"projectName": "nodejs-s3", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    environment:\n      - AWS_ACCESS_KEY\n      - AWS_SECRET_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-s3 project with Defang."}, {"projectName": "golang-mongodb", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n        published: 8080\n    environment:\n      # If you want to use MongoDB Atlas, you can set the URI with `defang config set MONGO_URI`\n      # and remove the value from the MONGO_URI environment variable so that it is read from defang\n      # config. For example:\n      # - MONGO_URI\n      - MONGO_URI=**************************************************************************/\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n    depends_on:\n      - db\n\n  db:\n    image: mongo:8.0.3\n    restart: unless-stopped\n    ports:\n      - mode: host\n        target: 27017\n    environment:\n      - MONGO_INITDB_ROOT_USERNAME\n      - MONGO_INITDB_ROOT_PASSWORD\n", "dockerfile": "# Start from the official Go image.\nFROM golang:1.20 as builder\n\n# Set the Current Working Directory inside the container\nWORKDIR /app\n\n# Copy go mod and sum files\nCOPY go.mod go.sum ./\n\n# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed\nRUN go mod download\n\n# Copy the source code into the container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o taskmanager .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the Pre-built binary file from the previous stage\nCOPY --from=builder /app/taskmanager .\nCOPY --from=builder /app/static ./static\n\n# Expose port 8080 to the outside world\nEXPOSE 8080\n\n# Command to run the executable\nCMD [\"./taskmanager\"]\n", "technologies": ["MongoDB", "Go"], "description": "A MongoDB, Go application that demonstrates how to deploy a golang-mongodb project with Defang."}, {"projectName": "fastapi-postgres", "compose": "services:\n  fastapi:\n    restart: unless-stopped\n    build:\n      context: ./fastapi\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8000\n        published: 8000\n    environment:\n      - DB_URL=postgres://postgres:${POSTGRES_PASSWORD}@db:5432/postgres\n    depends_on:\n      - db\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\n  db:\n    image: postgres:15\n    restart: unless-stopped\n    environment:\n      - POSTGRES_USER=postgres\n      - POSTGRES_PASSWORD\n      - POSTGRES_DB=postgres\n    ports:\n      - mode: host\n        target: 5432\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 8000\n\n# Run main when the container launches\nCMD [\"uvicorn\", \"main:app\", \"--host\", \"0.0.0.0\", \"--port\", \"8000\"]\n", "technologies": ["Python", "PostgreSQL"], "description": "A Python, PostgreSQL application that demonstrates how to deploy a fastapi-postgres project with Defang."}, {"projectName": "react", "compose": "services:\n  app:\n    restart: 'unless-stopped'\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 5173\n        published: 5173\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          memory: 512M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5173/\"]", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a react project with Defang."}, {"projectName": "python-rest-api", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 5000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-rest-api project with Defang."}, {"projectName": "elysia", "compose": "services:\n  elysia:\n    restart: unless-stopped\n    # domainname: mydomain.com # if you want to use your own domain\n    build:\n      context: ./elysia\n      dockerfile: Dockerfile\n      target: production\n    ports:\n      - target: 3000\n        mode: ingress\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          cpus: '0.50'\n          memory: 512M", "dockerfile": "FROM oven/bun:1.1-slim as bun-base\n\nWORKDIR /app\n\nCOPY package.json bun.lockb ./\nRUN bun install\n\n\nFROM bun-base as production\n\nCOPY package.json bun.lockb ./\nRUN bun install --production\n\nCOPY src src\nCOPY tsconfig.json ./\n\nENV NODE_ENV=production\n\nCMD [ \"bun\", \"src/index.ts\" ]", "technologies": [], "description": "A  application that demonstrates how to deploy a elysia project with Defang."}, {"projectName": "python-form", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 5000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-form project with Defang."}, {"projectName": "golang-openai", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    environment:\n      - OPENAI_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 50M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod go.sum ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nENTRYPOINT [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-openai project with Defang."}, {"projectName": "<PERSON><PERSON><PERSON>", "compose": "services:\n  fastapi:\n    restart: always\n    build:\n      context: ./fastapi\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8000\n        published: 8000\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 8000\n\n# Run main when the container launches\nCMD [\"uvicorn\", \"main:app\", \"--host\", \"0.0.0.0\", \"--port\", \"8000\"]\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a fastapi project with Defang."}, {"projectName": "langchain", "compose": "services:\n  langchain-app:\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    environment:\n      - OPENAI_KEY\n    ports:\n      - target: 80\n        published: 8080\n        protocol: tcp\n        mode: ingress\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\nRUN apt-get update -qq \\\n      && apt-get install -y curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Set the working directory in the container\nWORKDIR /app\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Install any needed packages specified in requirements.txt\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Make port 80 available to the world outside this container\nEXPOSE 80\n\n# Run main.py when the container launches\nCMD [\"python\", \"main.py\"]\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a langchain project with Defang."}, {"projectName": "django-postgres", "compose": "services:\n  db:\n    restart: unless-stopped\n    image: postgres:16\n    x-defang-postgres: true\n    environment:\n      - POSTGRES_DB=django\n      - POSTGRES_USER=django\n      - POSTGRES_PASSWORD\n    ports:\n      - mode: host\n        target: 5432\n        published: 5432\n\n  django:\n    # uncomment to add your own domain\n    # domainname: example.com\n    restart: unless-stopped\n    build: ./app\n    ports:\n      - mode: ingress\n        target: 8000\n        published: 8000\n    environment:\n      - DB_HOST=db\n      - DEBUG=False\n      - POSTGRES_USER=django\n      - POSTGRES_DB=django\n      - POSTGRES_PASSWORD\n      - SECRET_KEY\n      - ALLOWED_HOSTS\n    depends_on:\n      - db\n", "dockerfile": "FROM mcr.microsoft.com/devcontainers/python:3.12-bookworm\n", "technologies": ["PostgreSQL"], "description": "A PostgreSQL application that demonstrates how to deploy a django-postgres project with Defang."}, {"projectName": "nodejs-react-postgres", "compose": "services:\n  todo_list:\n    restart: unless-stopped\n    build:\n      context: ./client\n      dockerfile: Dockerfile\n      args:\n        - REACT_APP_API_URL=https://<YOUR_USERNAME>-server--3010.prod1.defang.dev #replace <YOUR_USERNAME> with your username\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 2GB\n    depends_on:\n      - server\n  server:\n    restart: unless-stopped\n    build:\n      context: ./server\n      dockerfile: Dockerfile\n    ports:\n      - target: 3010\n        mode: ingress\n        published: 3010\n    environment:\n     - DATABASE_URL    # To use your production database connection string, use `defang config set DATABASE_URL`\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-react-postgres project with Defang."}, {"projectName": "golang-s3", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    environment:\n      - AWS_ACCESS_KEY\n      - AWS_SECRET_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 50M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod go.sum ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nENTRYPOINT [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-s3 project with Defang."}, {"projectName": "django", "compose": "services:\n  django:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8000\n        # published: 8000 # uncomment for local dev\n    environment:\n      - DEBUG=False # set to \"True\" for local dev\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\nRUN apt-get update -qq \\\n  && apt-get install -y curl \\\n  && apt-get clean \\\n  && rm -rf /var/lib/apt/lists/*\n\n# Set environment variables\nENV PYTHONDONTWRITEBYTECODE 1\nENV PYTHONUNBUFFERED 1\n\n# Set work directory\nWORKDIR /code\n\n# Install dependencies\nCOPY requirements.txt /code/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy project\nCOPY . /code/\n\n# Collect static files\nRUN python manage.py collectstatic --noinput\n\n# Start server\nCMD python manage.py migrate && python manage.py createsuperauto && gunicorn defang_sample.wsgi:application --bind 0.0.0.0:8000\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a django project with Defang."}, {"projectName": "nextjs-claude", "compose": "services:\n  nextjs-claude:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./ui\n      dockerfile: Dockerfile\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    environment: \n      - ANTHROPIC_API_KEY\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "FROM node:bookworm-slim\n\nRUN apt-get update && apt-get install -y curl && apt-get clean\n\nWORKDIR /app\n\nCOPY package.json package-lock.json ./\n\nRUN npm ci\n\nCOPY . .\n\nRUN npm run build\n\nEXPOSE 3000\n\nCMD [ \"npm\", \"run\", \"start\" ]", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a nextjs-claude project with Defang."}, {"projectName": "javalin", "compose": "services:\n  javalin-app:\n    build:\n      context: ./app\n    ports:\n      - target: 7000\n        published: 7000\n        mode: ingress\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:7000/\"]\n", "dockerfile": "FROM mcr.microsoft.com/devcontainers/java:11-bookworm\n\n# Install Maven\nRUN apt-get update \\\n    && export DEBIAN_FRONTEND=noninteractive \\\n    && apt-get -y install --no-install-recommends maven \\\n    && apt-get clean \\\n    && rm -rf /var/lib/apt/lists/*\n", "technologies": [], "description": "A  application that demonstrates how to deploy a javalin project with Defang."}, {"projectName": "csharp-dotnet", "compose": "services:\n  web:\n    image: csharp-dotnet\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 80\n        published: 8080\n        mode: ingress\n", "dockerfile": "# Use the official .NET SDK image for building the application\nFROM mcr.microsoft.com/dotnet/sdk:7.0 AS build-env\nWORKDIR /app\n\n# Copy csproj and restore as distinct layers\nCOPY *.csproj ./\nRUN dotnet restore\n\n# Copy everything else and build\nCOPY . ./\nRUN dotnet publish -c Release -o out\n\n# Use the official .NET runtime image for running the application\nFROM mcr.microsoft.com/dotnet/aspnet:7.0\nWORKDIR /app\nCOPY --from=build-env /app/out .\n\n# Expose port 80\nEXPOSE 80\n\n# Start the application\nENTRYPOINT [\"dotnet\", \"csharp-dotnet.dll\"]\n", "technologies": [], "description": "A  application that demonstrates how to deploy a csharp-dotnet project with Defang."}, {"projectName": "rocket", "compose": "services:\n  app:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 8080\n        published: 8080\n        mode: ingress\n    healthcheck:\n      # wget or curl required for healthchecks on services with a published port\n      # this gets parsed by Defang and provided to the load balancers as well\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080\" ]", "dockerfile": "FROM mcr.microsoft.com/devcontainers/rust:1-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a rocket project with Defang."}, {"projectName": "pulumi-remix-postgres", "compose": "", "dockerfile": "FROM node:20-alpine\n\nWORKDIR /app\n\nCOPY package.json package-lock.json ./\n\nRUN npm ci\n\nCOPY . .\n\nRUN npm run build\n\nEXPOSE 3000\n\nCMD [ \"npm\", \"run\", \"start\" ]", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a pulumi-remix-postgres project with Defang."}, {"projectName": "nodejs-http", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-http project with Defang."}, {"projectName": "arduino-wifi", "compose": "services:\n  flask:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    deploy:\n      resources:\n        reservations:\n          cpus: \"1.0\"\n          memory: 512M\n    ports:\n      - mode: ingress\n        target: 8081\n        published: 8081\n    healthcheck:\n       test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request; urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:8081/\"]\n", "dockerfile": "# Use an official Python runtime as a base image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required packages\nRUN apt-get update -qq \\\n      && apt-get install -y --no-install-recommends \\\n      build-essential \\\n      python3-dev \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir --upgrade pip && pip install --no-cache-dir -r requirements.txt\n\n# Make port available to the world outside this container\nEXPOSE 8081\n\n# Run app.py when the container launches\nCMD [\"python3\", \"web_server.py\"]\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a arduino-wifi project with Defang."}, {"projectName": "<PERSON><PERSON><PERSON>", "compose": "services:\n  app:\n    build:\n      context: ./src\n      dockerfile: Dockerfile\n    ports:\n      - target: 3030\n        published: 3030\n        protocol: tcp\n        mode: ingress\n    environment:\n      - NODE_ENV=production\n", "dockerfile": "# Use the slim version of Node.js on Debian Bookworm as the base image\nFROM node:20-bookworm-slim\n\n# Set the working directory\nWORKDIR /usr/src/app\n\n# Copy package.json and package-lock.json if available\nCOPY package*.json ./\n\n# Install dependencies\nRUN npm install\n\n# Copy the rest of the application source code\nCOPY . ./\n\n# Expose port 3030\nEXPOSE 3030\n\n# Define the command to run the application\nCMD [\"node\", \"index.js\"]\n", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a feathersjs project with Defang."}, {"projectName": "nodejs-chatroom", "compose": "services:\n  chat:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-chatroom project with Defang."}, {"projectName": "<PERSON><PERSON>", "compose": "services:\n  hasura:\n    restart: unless-stopped\n    build:\n      dockerfile: Dockerfile\n      context: ./hasura\n    # If you want to add your own domain name in Defang BYOC, uncomment the following line and replace the domain name with your own\n    # domainname: hasura.mycoolapp.com\n    ports:\n      - target: 8080\n        published: 8080\n        mode: ingress\n    environment:\n      - HASURA_GRAPHQL_DATABASE_URL\n      - HASURA_GRAPHQL_ADMIN_SECRET\n      - HASURA_GRAPHQL_ENABLE_CONSOLE=true\n      - HASURA_GRAPHQL_UNAUTHORIZED_ROLE=public\n      - HASURA_GRAPHQL_EXPERIMENTAL_FEATURES=naming_convention\n      - HASURA_GRAPHQL_DEFAULT_NAMING_CONVENTION=graphql-default\n      - HASURA_GRAPHQL_MIGRATIONS_DIR=/hasura/migrations\n      - HASURA_GRAPHQL_METADATA_DIR=/hasura/metadata\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n", "dockerfile": "FROM hasura/graphql-engine:v2.39.0.cli-migrations-v3.ubuntu\n\nCOPY . /hasura", "technologies": [], "description": "A  application that demonstrates how to deploy a hasura project with Defang."}, {"projectName": "django-celery", "compose": "services:\n  web:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    environment:\n      - SECRET_KEY\n      - REDIS_URL=redis://broker:6379/0\n      - POSTGRES_URL=postgres://postgres:${POSTGRES_PASSWORD}@database:5432/postgres\n    depends_on:\n      - database\n      - broker\n    ports:\n      - mode: ingress\n        target: 8000\n        published: 8000\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8000/health/\"]\n      interval: 30s\n      timeout: 10s\n      retries: 3\n      start_period: 20s\n\n  worker:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    # If you need more than one worker, uncomment the following lines\n    # deploy:\n    #   replicas: 2\n    environment:\n      - SECRET_KEY\n      - REDIS_URL=redis://broker:6379/0\n      - POSTGRES_URL=postgres://postgres:${POSTGRES_PASSWORD}@database:5432/postgres\n    depends_on:\n      - database\n      - broker\n    command: celery -A django_celery worker --loglevel=info\n\n  database:\n    image: postgres:16\n    x-defang-postgres: true\n    ports:\n      - mode: host\n        target: 5432\n        published: 5432\n    environment:\n      - POSTGRES_PASSWORD\n    healthcheck:\n      test: [\"CMD-SHELL\", \"pg_isready -U postgres\"]\n      interval: 10s\n      timeout: 5s\n      retries: 5\n\n  broker:\n    image: redis:7\n    x-defang-redis: true\n    ports:\n      - mode: host\n        target: 6379\n        published: 6379\n    healthcheck:\n      test: [\"CMD\", \"redis-cli\", \"ping\"]\n      interval: 10s\n      timeout: 5s\n      retries: 5\n\n", "dockerfile": "FROM mcr.microsoft.com/devcontainers/python:3.12-bookworm\n", "technologies": ["PostgreSQL", "Redis"], "description": "A PostgreSQL, Redis application that demonstrates how to deploy a django-celery project with Defang."}, {"projectName": "hug<PERSON>", "compose": "services:\n  huginn:\n    image: ghcr.io/huginn/huginn\n    restart: unless-stopped\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          cpus: '1.0'\n          memory: 1G\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n      interval: 1m30s\n      timeout: 30s\n      retries: 10\n    environment:\n      - DATABASE_ADAPTER=postgresql\n      - DATABASE_NAME=huginn\n      - DATABASE_USERNAME=huginn\n      - DATABASE_HOST=db\n      - DATABASE_PASSWORD=\"${POSTGRES_PASSWORD}\"\n    depends_on:\n      - db\n  db:\n    image: postgres:16\n    environment:\n      - POSTGRES_USER=huginn\n      - POSTGRES_DB=huginn\n      - POSTGRES_PASSWORD\n    ports:\n      - mode: host\n        target: 5432\n        published: 5432\n", "dockerfile": "", "technologies": ["PostgreSQL"], "description": "A PostgreSQL application that demonstrates how to deploy a huginn project with Defang."}, {"projectName": "golang-rest-api", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    deploy:\n      resources:\n        reservations:\n          memory: 50M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod go.sum ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nENTRYPOINT [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-rest-api project with Defang."}, {"projectName": "nextjs-documentation", "compose": "services:\n  documentstarter:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 1GB\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nextjs-documentation project with Defang."}, {"projectName": "ollama", "compose": "services:\n  ollama:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./ollama\n      dockerfile: Dockerfile\n      shm_size: \"16gb\"\n    ports:\n      - target: 8000\n        mode: host\n    deploy:\n      resources:\n        reservations:\n          cpus: '2.0'\n          memory: 8192M\n          # Uncomment the next two lines to enable GPU support, for example to use gemma:7b\n          # NOTE: this is only supported in [BYOC](https://docs.defang.io/docs/concepts/defang-byoc)\n          # devices:\n          #   - capabilities: [\"gpu\"]\n    # By Default we load llama3.2:1b because it can run efficiently on a CPU, but you can select\n    # a different model by setting the LOAD_MODEL environment variable. Check the [list](https://ollama.com/library)\n    # for more models. For example, to load gemma:7b, set the LOAD_MODEL environment variable to gemma:7b below.\n    environment:\n      - LOAD_MODEL=llama3.2:1b\n      # - LOAD_MODEL=gemma:7b\n    healthcheck:\n      # wget or curl required for healthchecks on services with a published port\n      # this gets parsed by Defang and provided to the load balancers as well\n      test: [ \"CMD\", \"curl\", \"-s\", \"http://localhost:8000/\" ]\n\n  ui:\n    build:\n      context: ui\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      # wget or curl required for healthchecks on services with a published port\n      # this gets parsed by Defang and provided to the load balancers as well\n      test: [ \"CMD\", \"curl\", \"-s\", \"http://localhost:3000/\" ]\n    environment:\n      - OLLAMA_ENDPOINT=http://ollama:8000\n      # Make sure the LOAD_MODEL is the same as the app service\n      - LOAD_MODEL=llama3.2:1b\n      # - LOAD_MODEL=gemma:7b\n", "dockerfile": "FROM node:bookworm-slim\n\nRUN apt-get update && apt-get install -y curl && apt-get clean\n\nWORKDIR /app\n\nCOPY package.json package-lock.json ./\n\nRUN npm ci\n\nCOPY . .\n\nRUN npm run build\n\nEXPOSE 3000\n\nCMD [ \"npm\", \"run\", \"start\" ]", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a ollama project with Defang."}, {"projectName": "angular-express", "compose": "services:\n  angular-frontend:\n    build:\n      context: ./frontend\n    ports:\n      - target: 80\n        published: 80\n        mode: ingress\n    restart: unless-stopped\n    deploy:\n      resources:\n        reservations:\n          memory: 64M\n\n  backend:\n    build:\n      context: ./backend\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    environment:\n      - PORT=3000\n    restart: unless-stopped\n    deploy:\n      resources:\n        reservations:\n          memory: 64M\n    command: npm run prod\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a angular-express project with Defang."}, {"projectName": "nodejs-express", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-express project with Defang."}, {"projectName": "v<PERSON><PERSON><PERSON>", "compose": "services:\n  app: \n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 5173\n        published: 5173\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          memory: 512M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5173/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a vuejs project with Defang."}, {"projectName": "managed-llm-provider", "compose": "services:\n  app:\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - \"8000:8000\"\n    restart: always\n    environment:\n      - LLM_MODEL=default\n      # For other models, see https://docs.defang.io/docs/concepts/managed-llms/openai-access-gateway#model-mapping\n    healthcheck:\n      test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request; urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:8000/\"]\n      interval: 30s\n      timeout: 5s\n      retries: 3\n      start_period: 5s\n    depends_on:\n      - llm\n\n# Provider Service\n# This service is used to route requests to the LLM API\n  llm:\n    provider:\n      type: model\n      options:\n        model: ai/smollm2\n    x-defang-llm: true\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/python:alpine3.13\n", "technologies": [], "description": "A  application that demonstrates how to deploy a managed-llm-provider project with Defang."}, {"projectName": "pulumi", "compose": "", "dockerfile": "# Use an official Node runtime based on Alpine as a parent image\nFROM node:18-alpine\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Copy the current directory contents into the container at /app\nCOPY . .\n\n# Run the app when the container launches\nENTRYPOINT [ \"node\", \"main.js\" ]\n", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a pulumi project with Defang."}, {"projectName": "rails", "compose": "services:\n  rails:\n    # uncomment to add your own domain\n    # domainname: example.com\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    env_file:\n      - .env\n    environment:\n      POSTGRES_PASSWORD:\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 1GB\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000\"]\n      interval: 10s\n      timeout: 5s\n      retries: 5\n  db:\n    restart: unless-stopped\n    image: postgres:16\n    x-defang-postgres: true\n    environment:\n      POSTGRES_PASSWORD:\n    ports:\n      - mode: host\n        target: 5432\n    healthcheck:\n      test: [\"CMD-SHELL\", \"pg_isready -U postgres\"]\n      interval: 10s\n      timeout: 5s\n      retries: 5\n    deploy:\n      resources:\n        reservations:\n          memory: 1GB\n", "dockerfile": "ARG RUBY_VERSION=3.3.4\nFROM ghcr.io/rails/devcontainer/images/ruby:$RUBY_VERSION\n\nRUN apt-get update -qq \\\n      && apt-get install -y postgresql-client libpq-dev \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n", "technologies": ["PostgreSQL"], "description": "A PostgreSQL application that demonstrates how to deploy a rails project with Defang."}, {"projectName": "golang-http-form", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    deploy:\n      resources:\n        reservations:\n          memory: 50M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod go.sum ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nENTRYPOINT [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-http-form project with Defang."}, {"projectName": "phoenix-postgres", "compose": "services:\n  phoenix:\n    restart: unless-stopped\n    build:\n      context: ./server\n      dockerfile: Dockerfile\n    # uncomment to add your own domain\n    # domainname: mydomain.com\n    ports:\n      - target: 4000\n        mode: ingress\n        published: 4000\n    deploy:\n      resources:\n        reservations:\n          cpus: '0.5'\n          memory: 1024M\n    environment:\n      - PORT=4000\n      - SECRET_KEY_BASE\n      - DATABASE_URL", "dockerfile": "# Find eligible builder and runner images on Docker Hub. We use Ubuntu/Debian\n# instead of Alpine to avoid DNS resolution issues in production.\n#\n# https://hub.docker.com/r/hexpm/elixir/tags?page=1&name=ubuntu\n# https://hub.docker.com/_/ubuntu?tab=tags\n#\n# This file is based on these images:\n#\n#   - https://hub.docker.com/r/hexpm/elixir/tags - for the build image\n#   - https://hub.docker.com/_/debian?tab=tags&page=1&name=bullseye-20240513-slim - for the release image\n#   - https://pkgs.org/ - resource for finding needed packages\n#   - Ex: hexpm/elixir:1.16.2-erlang-24.0.6-debian-bullseye-20240513-slim\n#\nARG ELIXIR_VERSION=1.16.2\nARG OTP_VERSION=24.0.6\nARG DEBIAN_VERSION=bullseye-20240513-slim\n\nARG BUILDER_IMAGE=\"hexpm/elixir:${ELIXIR_VERSION}-erlang-${OTP_VERSION}-debian-${DEBIAN_VERSION}\"\nARG RUNNER_IMAGE=\"debian:${DEBIAN_VERSION}\"\n\nFROM ${BUILDER_IMAGE} as builder\n\n# install build dependencies\nRUN apt-get update -qq \\\n    && apt-get install -y build-essential git \\\n    && apt-get clean \\\n    && rm -f /var/lib/apt/lists/*\n\n# prepare build dir\nWORKDIR /app\n\n# install hex + rebar\nRUN mix local.hex --force && \\\n    mix local.rebar --force\n\n# set build ENV\nENV MIX_ENV=\"prod\"\n\n# install mix dependencies\nCOPY mix.exs mix.lock ./\nRUN mix deps.get --only $MIX_ENV\nRUN mkdir config\n\n# copy compile-time config files before we compile dependencies\n# to ensure any relevant config change will trigger the dependencies\n# to be re-compiled.\nCOPY config/config.exs config/${MIX_ENV}.exs config/\nRUN mix deps.compile\n\nCOPY priv priv\n\nCOPY lib lib\n\nCOPY assets assets\n\n# compile assets\nRUN mix assets.deploy\n\n# Compile the release\nRUN mix compile\n\n# Changes to config/runtime.exs don't require recompiling the code\nCOPY config/runtime.exs config/\n\nCOPY rel rel\nRUN mix release\n\n# start a new build stage so that the final image will only contain\n# the compiled release and other runtime necessities\nFROM ${RUNNER_IMAGE}\n\nRUN apt-get update && \\\n  apt-get install -y libstdc++6 openssl libncurses5 locales ca-certificates \\\n  && apt-get clean \\\n  && rm -f /var/lib/apt/lists/*\n\n# Set the locale\nRUN sed -i '/en_US.UTF-8/s/^# //g' /etc/locale.gen && locale-gen\n\nENV LANG en_US.UTF-8\nENV LANGUAGE en_US:en\nENV LC_ALL en_US.UTF-8\n\nWORKDIR \"/app\"\nRUN chown nobody /app\n\n# set runner ENV\nENV MIX_ENV=\"prod\"\n\n# Only copy the final release from the build stage\nCOPY --from=builder --chown=nobody:root /app/_build/${MIX_ENV}/rel/defang ./\n\nCOPY --chown=nobody:root ./scripts/prod.sh ./prod.sh\n\nUSER nobody\n\n# If using an environment that doesn't automatically reap zombie processes, it is\n# advised to add an init process such as tini via `apt-get install`\n# above and adding an entrypoint. See https://github.com/krallin/tini for details\n# ENTRYPOINT [\"/tini\", \"--\"]\n\nCMD [\"/app/prod.sh\"]\n", "technologies": [], "description": "A  application that demonstrates how to deploy a phoenix-postgres project with Defang."}, {"projectName": "sveltekit-mongodb", "compose": "services:\n  sveltekit:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n      args:\n        MONGODB_URI: \"mongodb://mongo:27017/musicdb\"\n    environment:\n      NODE_ENV: production\n      HOST: \"0.0.0.0\"\n      PORT: 3000\n      MONGODB_URI: \"mongodb://mongo:27017/musicdb\"\n    ports:\n      - \"3000:3000\"\n    depends_on:\n      - mongo\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n\n  mongo:\n    restart: unless-stopped\n    image: mongo:latest\n    ports:\n      - mode: host\n        target: 27017\n    volumes:\n      - mongo-data:/data/db\n    environment:\n      MONGO_INITDB_DATABASE: musicdb\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\nvolumes:\n  mongo-data:\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": ["MongoDB"], "description": "A MongoDB application that demonstrates how to deploy a sveltekit-mongodb project with Defang."}, {"projectName": "python-minimal", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 5000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-minimal project with Defang."}, {"projectName": "python-implicit-gpu", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    deploy:\n      resources:\n        reservations:\n          cpus: \"1.0\"\n          memory: 2048M\n          devices:\n            - capabilities: [\"gpu\"]\n    ports:\n      - mode: ingress\n        target: 5000\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-implicit-gpu project with Defang."}, {"projectName": "nextjs-blog", "compose": "services:\n  blogstarter:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 1GB\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nextjs-blog project with Defang."}, {"projectName": "nocodb", "compose": "services:\n  nocodb:\n    image: \"nocodb/nocodb:0.252.0\"\n    environment:\n      NC_DB: \n      NC_S3_ENDPOINT:\n      NC_S3_BUCKET_NAME:\n      NC_S3_REGION:\n      NC_S3_ACCESS_KEY:\n      NC_S3_ACCESS_SECRET:\n    ports:\n      - target: 8080\n        published: 8080\n        mode: ingress\n", "dockerfile": "", "technologies": [], "description": "A  application that demonstrates how to deploy a nocodb project with Defang."}, {"projectName": "nounly", "compose": "name: nounly\nservices:\n  nounly:\n    restart: always\n    build:\n      context: .\n    deploy:\n      resources:\n        reservations:\n          memory: 512M\n    healthcheck:\n      test:\n        [\"CMD\", \"wget\", \"--spider\", \"-qO-\", \"http://localhost:2345/v1/status\"]\n    ports:\n      - 2345:2345\n    environment:\n      PORT: 2345\n      REDIS: redis:6379\n      PROJECT_HONEYPOT_KEY:\n      SHARED_SECRETS: \"{}\"\n\n  redis:\n    restart: always\n    image: redis:alpine\n    deploy:\n      resources:\n        reservations:\n          memory: 512M\n    ports:\n      - target: 6379\n        published: 6379\n        protocol: tcp\n        mode: host\n    platform: linux/arm64\n    x-defang-redis: true\n", "dockerfile": "# syntax=docker/dockerfile:1.4\nARG GOVERSION=1.22\nARG BASE=busybox # for wget healtcheck\n\nFROM --platform=${BUILDPLATFORM} golang:${GOVERSION} AS build\n# These two are automatically set by docker buildx\nARG TARGETARCH\nARG TARGETOS\nWORKDIR /src\nCOPY --link go.mod go.sum* ./\nARG GOPRIVATE=github.com/lionello/nounly-go\nRUN go mod download\nARG GOSRC=.\nCOPY --link ${GOSRC} ./\n# RUN go test -v ./... FIXME: \"no required module provides package github.com/defang-io/defang-mvp/fabric/internal/util_test\"\nARG BUILD=./cmd/server\nARG VERSION\nRUN CGO_ENABLED=0 GOOS=${TARGETOS} GOARCH=${TARGETARCH} go build -trimpath -buildvcs=false -ldflags=\"-w -s -X \\\"main.version=${VERSION}\\\"\" -o /server \"${BUILD}\"\n\nFROM --platform=${TARGETPLATFORM} ${BASE}\n# RUN apk add --update curl ca-certificates && rm -rf /var/cache/apk* # Certificates for SSL\nARG PORT=80\nENV PORT=$PORT\nCOPY --link --from=build /server /server\nCOPY --link ./public /public\nENTRYPOINT [ \"/server\" ]\nEXPOSE $PORT\n# USER nobody\n", "technologies": ["Redis"], "description": "A Redis application that demonstrates how to deploy a nounly project with Defang."}, {"projectName": "nodejs-form", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-form project with Defang."}, {"projectName": "nextjs", "compose": "services:\n  app:\n    restart: unless-stopped\n    # domainname: mydomain.com # if you want to use your own domain\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n\n    ports:\n      - target: 3000\n        mode: ingress\n        published: 3000\n\n    healthcheck:\n      test: wget -q --spider http://localhost:3000/ || exit 1\n\n    deploy:\n      resources:\n        reservations:\n          cpus: '0.50'\n          memory: 512M\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nextjs project with Defang."}, {"projectName": "python-s3", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 5000\n    environment:\n      - AWS_ACCESS_KEY\n      - AWS_SECRET_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-s3 project with Defang."}, {"projectName": "metabase", "compose": "services:\n  metabase:\n    restart: unless-stopped\n    image: \"metabase/metabase:latest\"\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/api/health\" ]\n      interval: 1m30s\n      timeout: 30s\n      retries: 5\n    environment:\n      - MB_DB_TYPE=postgres\n      - MB_DB_DBNAME\n      - MB_DB_PORT\n      - MB_DB_USER\n      - MB_DB_PASS\n      - MB_DB_HOST\n    deploy:\n      resources:\n        reservations:\n          cpus: \"0.5\"\n          memory: 1024M", "dockerfile": "", "technologies": ["PostgreSQL"], "description": "A PostgreSQL application that demonstrates how to deploy a metabase project with Defang."}, {"projectName": "nextjs-postg<PERSON>", "compose": "services:\n  app:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    environment:\n      POSTGRES_USER: postgres\n      POSTGRES_PASSWORD:\n      POSTGRES_HOST: database\n      POSTGRES_PORT: 5432\n      POSTGRES_DB: postgres\n      POSTGRES_SSL: true\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000\" ]\n    depends_on:\n      - database\n\n  database:\n    image: postgres:16\n    x-defang-postgres: true\n    environment:\n      POSTGRES_PASSWORD:\n    ports:\n      - target: 5432\n        published: 5432\n        mode: host", "dockerfile": "FROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": ["PostgreSQL"], "description": "A PostgreSQL application that demonstrates how to deploy a nextjs-postgres project with Defang."}, {"projectName": "html-css-js", "compose": "services:\n  app:\n    # uncomment to add your own domain\n    # domainname: example.com\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports: \n      - target: 8080\n        published: 8080\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a html-css-js project with Defang."}, {"projectName": "sailsjs-postgres", "compose": "services:\n  app:\n    restart: unless-stopped\n    build: ./app\n    ports:\n      - target: 1337\n        published: 1337\n        mode: ingress\n    environment:\n      DATABASE_URL:\n      SESSION_SECRET:\n    command: [\"sails\", \"lift\", \"--prod\"]\n    networks:\n      - app-network\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:1337/\"]\n\nnetworks:\n  app-network:\n    driver: bridge\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a sailsjs-postgres project with Defang."}, {"projectName": "svelte-mysql", "compose": "services:\n  db:\n    restart: unless-stopped\n    image: mysql:latest\n    environment:\n      MYSQL_ALLOW_EMPTY_PASSWORD: true\n      MYSQL_DATABASE: todoApp\n      MYSQL_USER: admin\n      MYSQL_PASSWORD: Defang12345\n    volumes:\n      - mysql_data:/var/lib/mysql\n    ports:\n      - mode: host\n        target: 3306\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\n  app:\n    restart: unless-stopped\n    build: ./app\n    environment:\n      DATABASE_HOST: db\n      DATABASE_USER: admin\n      DATABASE_PASSWORD: Defang12345\n      DATABASE_NAME: todoApp\n    ports:\n      - \"3001:3001\"\n    depends_on:\n      - db\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3001/\"]\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\nvolumes:\n  mysql_data:\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": ["MySQL"], "description": "A MySQL application that demonstrates how to deploy a svelte-mysql project with Defang."}, {"projectName": "sveltekit", "compose": "services:\n  sveltekit:\n    restart: unless-stopped\n    # domainname: mydomain.com # if you want to use your own domain\n    build:\n      context: ./sveltekit\n      dockerfile: Dockerfile\n    ports:\n      - target: 3000\n        mode: ingress\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          cpus: '0.50'\n          memory: 512M", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a sveltekit project with Defang."}, {"projectName": "vllm", "compose": "services:\n  mistral:\n    restart: unless-stopped\n    image: ghcr.io/mistralai/mistral-src/vllm:latest\n    ports:\n      - mode: host\n        target: 8000\n    command: [\"--host\", \"0.0.0.0\", \"--model\", \"TheBloke/Mistral-7B-Instruct-v0.2-AWQ\", \"--quantization\", \"awq\", \"--dtype\", \"auto\", \"--tensor-parallel-size\", \"1\", \"--gpu-memory-utilization\", \".95\", \"--max-model-len\", \"8000\"]\n    deploy:\n      resources:\n        reservations:\n          cpus: '2.0'\n          memory: 8192M\n          devices:\n            - capabilities: [\"gpu\"]\n              count: 1\n    healthcheck:\n      test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request;urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:8000/health\"]\n      interval: 1m\n    environment:\n      - HF_TOKEN\n  ui:\n    restart: unless-stopped\n    build:\n      context: ui\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"wget\", \"--spider\", \"http://localhost:3000\"]\n      interval: 10s\n      timeout: 2s\n      retries: 10\n    environment:\n      - OPENAI_BASE_URL=http://mistral:8000/v1/\n", "dockerfile": "FROM node:20-alpine\n\nWORKDIR /app\n\nCOPY package.json package-lock.json ./\n\nRUN npm ci\n\nCOPY . .\n\nRUN npm run build\n\nEXPOSE 3000\n\nCMD [ \"npm\", \"run\", \"start\" ]", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a vllm project with Defang."}, {"projectName": "mcp", "compose": "services:\n  service-1: # Web Server and UI\n    build:\n      context: ./service-1\n      dockerfile: Dockerfile\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    environment:\n      - MCP_SERVICE_URL=http://service-2:8000\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n\n  service-2: # MCP Service (MCP Client and Server)\n    build:\n      context: ./service-2\n      dockerfile: Dockerfile\n    ports:\n      - target: 8000\n        published: 8000\n        mode: host\n    environment:\n      - ANTHROPIC_API_KEY\n", "dockerfile": "FROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a mcp project with Defang."}, {"projectName": "<PERSON><PERSON>s", "compose": "services:\n  sails:\n    restart: unless-stopped\n    build:\n      context: ./sails\n    ports:\n      - target: 1337\n        published: 1337\n        protocol: tcp\n        mode: ingress\n    volumes:\n      - ./sails:/usr/src/app\n      - /usr/src/app/node_modules\n    environment:\n      - NODE_ENV=production\n    command: sails lift --prod\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a sailsjs project with Defang."}, {"projectName": "go<PERSON>-slackbot", "compose": "services:\n  bot:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    environment:\n      - SLACK_TOKEN\n      - SLACK_CHANNEL_ID\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nCMD [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-slackbot project with Defang."}, {"projectName": "nodejs-rest-api", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:3000/\"]\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/typescript-node:22-bookworm\n", "technologies": [], "description": "A  application that demonstrates how to deploy a nodejs-rest-api project with Defang."}, {"projectName": "django-channels-redis-postgres", "compose": "services:\n  app:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - target: 8000\n        published: 8000\n        mode: ingress\n    environment:\n      - REDIS_URL=redis://redis-service:6379\n      # if using BYOC add sslmode=require to the POSTGRES_URL query params\n      - POSTGRES_URL=postgres://djangouser:${POSTGRES_PASSWORD}@postgres-service:5432/djangodatabase?\n      - SECRET_KEY\n    healthcheck:\n      # wget or curl required for healthchecks on services with a published port\n      # this gets parsed by Defang and provided to the load balancers as well\n      test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request; urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:8000/\"]\n      interval: 10s\n    depends_on:\n      - redis-service\n      - postgres-service\n\n  redis-service:\n    image: redis:6.2\n    # uncomment the following line in BYOC\n    # x-defang-redis: true\n    ports:\n      - mode: host\n        target: 6379\n\n  postgres-service:\n    image: postgres:16.4\n    # uncomment the following line in BYOC\n    # x-defang-postgres: true\n    ports:\n      - mode: host\n        target: 5432\n    environment:\n      - POSTGRES_PASSWORD\n      - POSTGRES_DB=djangodatabase\n      - POSTGRES_USER=djangouser\n", "dockerfile": "FROM mcr.microsoft.com/devcontainers/python:3.12-bookworm\n\n# Install django cli\nRUN pip install django", "technologies": ["Django", "PostgreSQL", "Redis"], "description": "A Django, PostgreSQL, Redis application that demonstrates how to deploy a django-channels-redis-postgres project with Defang."}, {"projectName": "bullmq-bullboard-redis", "compose": "services:\n  board:\n    restart: unless-stopped\n    build:\n      context: ./board\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3000\n        published: 3000\n    environment:\n      - REDIS=redis://redisx:6379\n      - BOARD_PASSWORD\n      - QUEUE\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"127.0.0.1:3000\"]\n    depends_on:\n      - redisx\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\n  api:\n    restart: unless-stopped\n    build:\n      context: ./api\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 3001\n        published: 3001\n    environment:\n      - REDIS=redis://redisx:6379\n      - QUEUE\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"127.0.0.1:3001\"]\n    depends_on:\n      - redisx\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\n  worker:\n    restart: unless-stopped\n    deploy:\n      replicas: 1\n    build:\n      context: ./worker\n      dockerfile: Dockerfile\n    environment:\n      - REDIS=redis://redisx:6379\n      - QUEUE\n    depends_on:\n      - redisx\n    #deploy:\n    #  resources:\n    #    reservations:\n    #      memory: 256M\n\n  redisx:\n    image: redis:6.2\n    x-defang-redis: true\n    restart: unless-stopped\n    ports:\n      - mode: host\n        target: 6379\n", "dockerfile": "# Use the slim version of Node.js v20 on Debian Bookworm as the base image\nFROM node:20-bookworm-slim\n\n# Install tools for healthcheck\nRUN apt-get update && apt-get install -y \\\n    curl \\\n    && rm -rf /var/lib/apt/lists/*\n\n# Set the working directory\nWORKDIR /app\n\n# Copy package.json and package-lock.json\nCOPY package*.json ./\n\n# Install dependencies using npm ci for a clean install\nRUN npm ci\n\n# Copy the rest of the application source code\nCOPY . .\n\n# Define the command to run the application\nCMD [\"node\", \"index.mjs\"]\n", "technologies": ["Redis", "Node.js"], "description": "A Redis, Node.js application that demonstrates how to deploy a bullmq-bullboard-redis project with Defang."}, {"projectName": "nextjs-cv", "compose": "services:\n  app:\n    # uncomment to add your own domain\n    # domainname: example.com\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    restart: unless-stopped\n    ports:\n      - target: 3000\n        published: 3000\n        mode: ingress\n    environment: # Secrets and env vars\n     - NODE_ENV=production\n    healthcheck:\n      # wget or curl required for healthchecks on services with a published port\n      # this gets parsed by Defang and provided to the load balancers as well\n      test: [\"CMD\", \"wget\", \"--spider\", \"http://localhost:3000/\"]\n", "dockerfile": "FROM node:22.2.0-slim as BUILD_STAGE\n\nWORKDIR /app\n\nCOPY package.json yarn.lock ./\n\nRUN yarn install --frozen-lockfile\n\nCOPY . .\n\nRUN yarn build\n\nFROM node:alpine\n\nWORKDIR /app\n\nCOPY --from=BUILD_STAGE /app/package.json ./package.json\nCOPY --from=BUILD_STAGE /app/node_modules ./node_modules\nCOPY --from=BUILD_STAGE /app/.next ./.next\nCOPY --from=BUILD_STAGE /app/public ./public\n\nEXPOSE 3000\n\nCMD [\"yarn\", \"start\"]\n", "technologies": ["Node.js"], "description": "A Node.js application that demonstrates how to deploy a nextjs-cv project with Defang."}, {"projectName": "python-openai", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 5000\n    environment:\n      - OPENAI_KEY\n    deploy:\n      resources:\n        reservations:\n          memory: 256M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:5000/\"]\n", "dockerfile": "# Use an official Python runtime as a parent image\nFROM python:3.11-slim\n\n# Set the working directory to /app\nWORKDIR /app\n\n# Install required C++11 libraries and ca-certificates\nRUN apt-get update -qq \\\n      && apt-get install -y \\\n      build-essential \\\n      python3-dev \\\n      ca-certificates \\\n      curl \\\n      && apt-get clean \\\n      && rm -rf /var/lib/apt/lists/*\n\n# Install any needed packages specified in requirements.txt\nCOPY requirements.txt /app/\nRUN pip install --no-cache-dir -r requirements.txt\n\n# Copy the current directory contents into the container at /app\nCOPY . /app\n\n# Make port 5000 available to the world outside this container\nEXPOSE 5000\n\n# Run main when the container launches\nENTRYPOINT [\"uwsgi\", \"--http\", \"0.0.0.0:5000\", \"--master\", \"-p\", \"2\", \"-w\", \"main:app\"]\nUSER nobody\n", "technologies": ["Python"], "description": "A Python application that demonstrates how to deploy a python-openai project with Defang."}, {"projectName": "managed-llm", "compose": "services:\n  app:\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - \"8000:8000\"\n    restart: always\n    environment:\n      - LLM_URL=http://llm/api/v1/   # endpoint to the gateway service\n      - LLM_MODEL=default  # LLM model ID used for the gateway.\n       # For other models, see https://docs.defang.io/docs/concepts/managed-llms/openai-access-gateway#model-mapping\n      - OPENAI_API_KEY=FAKE_TOKEN   # the actual value will be ignored when using the gateway, but it should match the one in the llm service\n    healthcheck:\n      test: [\"CMD\", \"python3\", \"-c\", \"import sys, urllib.request; urllib.request.urlopen(sys.argv[1]).read()\", \"http://localhost:8000/\"]\n      interval: 30s\n      timeout: 5s\n      retries: 3\n      start_period: 5s\n    depends_on:\n      - llm\n    networks:\n      - default\n      - private\n\n# Defang OpenAI Access Gateway\n# This service is used to route requests to the LLM API\n  llm:\n    x-defang-llm: true # this must be set to true for the gateway to work\n    image: docker.io/defangio/openai-access-gateway:latest\n    ports:\n      - target: 80\n        published: 80\n        protocol: tcp\n        mode: host\n    networks:\n      - private\n    environment:\n      - OPENAI_API_KEY=FAKE_TOKEN # this value must match the one in the app service\n      # if using GCP for BYOC deployment, add these environment variables:\n      # - GCP_PROJECT_ID=${GCP_PROJECT_ID}\n      # - GCP_REGION=${GCP_REGION}\n\nnetworks:\n  default:\n  private:\n", "dockerfile": "\nFROM mcr.microsoft.com/devcontainers/python:alpine3.13\n", "technologies": [], "description": "A  application that demonstrates how to deploy a managed-llm project with Defang."}, {"projectName": "golang-http", "compose": "services:\n  app:\n    restart: unless-stopped\n    build:\n      context: ./app\n      dockerfile: Dockerfile\n    ports:\n      - mode: ingress\n        target: 8080\n    deploy:\n      resources:\n        reservations:\n          memory: 50M\n    healthcheck:\n      test: [\"CMD\", \"curl\", \"-f\", \"http://localhost:8080/\"]\n", "dockerfile": "# Use an official Go runtime as a parent image\nFROM golang:1.20 as builder\n\n# Set the working directory in the builder container\nWORKDIR /src\n\n# Copy go.mod and go.sum files to the workspace\nCOPY go.mod go.sum ./\n\n# Download all dependencies.\nRUN go mod download\n\n# Copy the source from the current directory to the working Directory in the builder container\nCOPY . .\n\n# Build the Go app\nRUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .\n\n# Start a new stage from scratch\nFROM golang:1.20\n\nWORKDIR /root/\n\n# Copy the binary from builder\nCOPY --from=builder /src/main .\n\n# Expose port 8080 to the world outside this container\nEXPOSE 8080\n\n# Run the binary\nENTRYPOINT [\"./main\"]\n", "technologies": ["Go"], "description": "A Go application that demonstrates how to deploy a golang-http project with Defang."}]