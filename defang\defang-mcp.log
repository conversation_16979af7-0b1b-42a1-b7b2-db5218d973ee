 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
Server shutdown
 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
Server shutdown
 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
Server shutdown
 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
Server shutdown
 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
 * Setting up knowledge base
 * Attempting to download knowledge base files: [knowledge_base.json samples_examples.json]
 * Creating knowledge base directory: defang
 * Downloading knowledge base file: knowledge_base.json
 * Creating file: defang/knowledge_base.json
 * Downloading file: https://ask.defang.io/data/knowledge_base.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/knowledge_base.json
 * Downloading knowledge base file: samples_examples.json
 * Creating file: defang/samples_examples.json
 * Downloading file: https://ask.defang.io/data/samples_examples.json
 * Checking server response: 200 OK
 * Copying Using IO Copy: defang/samples_examples.json
 * Successfully downloaded knowledge base files
 * Starting Defang MCP server
 * Creating MCP server
 * Creating documentation resource
 * Creating samples examples resource
 * Setting up login tool
 * Creating login tool
 * Adding login tool handler
 * Setting up services tool
 * Creating services tool
 * Adding services tool handler
 * Setting up deployment tool
 * Creating deployment tool
 * Adding deployment tool handler
 * Setting up destroy tool
 * Creating destroy tool
 * Adding destroy tool handler
 * All MCP tools have been set up successfully
Starting Defang MCP server
