<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plantilla Profesional GSAP & Lenis</title>
    <link rel="stylesheet" href="styles/main.css">
    <meta name="description" content="Plantilla web profesional con animaciones avanzadas GSAP y scroll suave con Lenis">
</head>
<body>
    <div id="preloader" class="preloader">
        <div class="loader-content">
            <svg class="loader" viewBox="0 0 100 100">
                <circle class="loader-bg" cx="50" cy="50" r="45"></circle>
                <circle class="loader-circle" cx="50" cy="50" r="45"></circle>
                <text class="loader-text" x="50" y="55">Loading</text>
            </svg>
        </div>
    </div>

    <header class="site-header">
        <nav class="navbar" role="navigation" aria-label="Menú principal">
            <div class="navbar-brand">
                <a href="#" class="logo">Mi<span>Marca</span></a>
            </div>
            <button class="menu-toggle" aria-controls="navbar-menu" aria-expanded="false">
                <span class="hamburger"></span>
            </button>
            <ul id="navbar-menu" class="navbar-menu" role="menu">
                <li class="menu-item"><a href="#hero" class="menu-link">Inicio</a></li>
                <li class="menu-item"><a href="#portfolio" class="menu-link">Portafolio</a></li>
                <li class="menu-item"><a href="#testimonials" class="menu-link">Testimonios</a></li>
                <li class="menu-item"><a href="#contact" class="menu-link">Contacto</a></li>
                <li class="menu-item dark-mode-toggle">
                    <button aria-label="Cambiar tema">
                        <svg class="moon" viewBox="0 0 24 24">
                            <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                        </svg>
                        <svg class="sun" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="5"></circle>
                            <line x1="12" y1="1" x2="12" y2="3"></line>
                            <line x1="12" y1="21" x2="12" y2="23"></line>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                            <line x1="1" y1="12" x2="3" y2="12"></line>
                            <line x1="21" y1="12" x2="23" y2="12"></line>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                        </svg>
                    </button>
                </li>
            </ul>
        </nav>
    </header>

    <main class="site-main">
        <section id="hero" class="hero-section">
            <div class="parallax-container">
                <div class="parallax-layer layer-1" data-speed="0.2">
                    <div class="background-shape shape-1"></div>
                </div>
                <div class="parallax-layer layer-2" data-speed="0.4">
                    <div class="background-shape shape-2"></div>
                </div>
                <div class="parallax-layer layer-3" data-speed="0.6">
                    <div class="background-shape shape-3"></div>
                </div>
                <div class="parallax-layer layer-4" data-speed="0.8">
                    <div class="hero-content">
                        <h1 class="hero-title">Diseño Web <span>Profesional</span></h1>
                        <p class="hero-subtitle">Creamos experiencias digitales que inspiran</p>
                        <a href="#portfolio" class="btn-primary">Ver Proyectos</a>
                    </div>
                </div>
            </div>
        </section>

        <section id="portfolio" class="portfolio-section">
            <div class="section-header">
                <h2 class="section-title">Nuestro <span>Portafolio</span></h2>
                <p class="section-description">Proyectos destacados que demuestran nuestra excelencia</p>
            </div>
            <div class="portfolio-grid">
                <div class="portfolio-item" data-category="web">
                    <div class="portfolio-image">
                        <img src="https://picsum.photos/seed/web1/600/400" alt="Proyecto Web 1" loading="lazy">
                        <div class="portfolio-overlay">
                            <div class="portfolio-info">
                                <h3>Proyecto Web</h3>
                                <p>Diseño & Desarrollo</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item" data-category="branding">
                    <div class="portfolio-image">
                        <img src="https://picsum.photos/seed/branding1/600/400" alt="Proyecto de Branding" loading="lazy">
                        <div class="portfolio-overlay">
                            <div class="portfolio-info">
                                <h3>Branding</h3>
                                <p>Identidad Corporativa</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="portfolio-item" data-category="app">
                    <div class="portfolio-image">
                        <img src="https://picsum.photos/seed/app1/600/400" alt="Aplicación Móvil" loading="lazy">
                        <div class="portfolio-overlay">
                            <div class="portfolio-info">
                                <h3>Aplicación</h3>
                                <p>Desarrollo Móvil</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="testimonials" class="testimonials-section">
            <div class="section-header">
                <h2 class="section-title">Lo que dicen <span>nuestros clientes</span></h2>
                <p class="section-description">Testimonios reales de colaboraciones exitosas</p>
            </div>
            <div class="testimonials-carousel">
                <div class="testimonial-item">
                    <div class="testimonial-content">
                        <p>"El equipo demostró un nivel excepcional de profesionalismo y creatividad. El resultado superó todas nuestras expectativas."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-image">
                            <img src="https://randomuser.me/api/portraits/women/75.jpg" alt="María González" loading="lazy">
                        </div>
                        <div class="testimonial-info">
                            <h4>María González</h4>
                            <p>Directora de Marketing</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-item">
                    <div class="testimonial-content">
                        <p>"La atención al detalle y el enfoque en la experiencia de usuario hicieron toda la diferencia en nuestro proyecto."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-image">
                            <img src="https://randomuser.me/api/portraits/men/68.jpg" alt="Carlos Rodríguez" loading="lazy">
                        </div>
                        <div class="testimonial-info">
                            <h4>Carlos Rodríguez</h4>
                            <p>CEO de TechStart</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-item">
                    <div class="testimonial-content">
                        <p>"Desde el primer contacto hasta la entrega final, todo fue absolutamente profesional y de alta calidad."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="testimonial-image">
                            <img src="https://randomuser.me/api/portraits/women/71.jpg" alt="Laura Martínez" loading="lazy">
                        </div>
                        <div class="testimonial-info">
                            <h4>Laura Martínez</h4>
                            <p>Gerente de Proyectos</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="contact" class="contact-section">
            <div class="section-header">
                <h2 class="section-title">Contáctanos <span>hoy</span></h2>
                <p class="section-description">Estamos listos para ayudarte con tu próximo proyecto</p>
            </div>
            <div class="contact-container">
                <div class="contact-info">
                    <div class="contact-item">
                        <svg viewBox="0 0 24 24">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                            <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                        <span>Ciudad de México, México</span>
                    </div>
                    <div class="contact-item">
                        <svg viewBox="0 0 24 24">
                            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                        </svg>
                        <span>+52 55 1234 5678</span>
                    </div>
                    <div class="contact-item">
                        <svg viewBox="0 0 24 24">
                            <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                            <polyline points="22,6 12,13 2,6"></polyline>
                        </svg>
                        <span><EMAIL></span>
                    </div>
                </div>
                <form class="contact-form" id="contactForm" novalidate>
                    <div class="form-group">
                        <label for="name">Nombre completo</label>
                        <input type="text" id="name" name="name" required aria-required="true">
                        <div class="form-error" aria-live="polite"></div>
                    </div>
                    <div class="form-group">
                        <label for="email">Correo electrónico</label>
                        <input type="email" id="email" name="email" required aria-required="true">
                        <div class="form-error" aria-live="polite"></div>
                    </div>
                    <div class="form-group">
                        <label for="message">Mensaje</label>
                        <textarea id="message" name="message" rows="5" required aria-required="true"></textarea>
                        <div class="form-error" aria-live="polite"></div>
                    </div>
                    <button type="submit" class="btn-primary">Enviar Mensaje</button>
                </form>
            </div>
        </section>
    </main>

    <footer class="site-footer">
        <div class="footer-content">
            <div class="footer-brand">
                <a href="#" class="logo">Mi<span>Marca</span></a>
                <p>Creando experiencias digitales excepcionales desde 2020</p>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook">
                        <svg viewBox="0 0 24 24">
                            <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                        </svg>
                    </a>
                    <a href="#" aria-label="Instagram">
                        <svg viewBox="0 0 24 24">
                            <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                            <circle cx="12" cy="12" r="3"></circle>
                            <circle cx="16.5" cy="7.5" r="1"></circle>
                        </svg>
                    </a>
                    <a href="#" aria-label="LinkedIn">
                        <svg viewBox="0 0 24 24">
                            <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                            <rect x="2" y="9" width="4" height="12"></rect>
                            <circle cx="4" cy="4" r="2"></circle>
                        </svg>
                    </a>
                </div>
            </div>
            <div class="footer-links">
                <h4>Enlaces Rápidos</h4>
                <ul>
                    <li><a href="#hero">Inicio</a></li>
                    <li><a href="#portfolio">Portafolio</a></li>
                    <li><a href="#testimonials">Testimonios</a></li>
                    <li><a href="#contact">Contacto</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h4>Contacto</h4>
                <p><strong>Teléfono:</strong> +52 55 1234 5678</p>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Dirección:</strong> Ciudad de México, México</p>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Mi Marca. Todos los derechos reservados.</p>
            <div class="footer-legal">
                <a href="#">Política de Privacidad</a>
                <a href="#">Términos de Servicio</a>
                <a href="#">Cookies</a>
            </div>
        </div>
    </footer>

    <div class="scroll-top" aria-label="Volver arriba">
        <svg viewBox="0 0 24 24">
            <polyline points="18 15 12 9 6 15"></polyline>
        </svg>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/gsap.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.12.2/dist/ScrollTrigger.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/lenis@1.0.19/unpkg/lenis.min.js"></script>
    <script src="scripts/main.js"></script>
</body>
</html>