document.addEventListener('DOMContentLoaded', function() {
  const toggleBtn = document.querySelector('.dark-mode-toggle button');
  const html = document.documentElement;

  // Initialize dark mode based on user preference
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  if (prefersDark) {
    html.classList.add('dark');
    document.querySelector('.moon')?.classList.add('hidden');
    document.querySelector('.sun')?.classList.remove('hidden');
  }

  toggleBtn.addEventListener('click', function() {
    html.classList.toggle('dark');
    
    // Toggle moon/sun icons
    const moon = document.querySelector('.moon');
    const sun = document.querySelector('.sun');
    
    if (moon && sun) {
      moon.classList.toggle('hidden');
      sun.classList.toggle('hidden');
      
      // Save user preference
      const isDark = html.classList.contains('dark');
      localStorage.setItem('theme', isDark ? 'dark' : 'light');
    }
  });

  // Add smooth transition for theme changes
  html.style.transition = 'background-color 0.3s ease, color 0.3s ease';
});