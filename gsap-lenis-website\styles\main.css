:root {
    /* Colors */
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #e056fd;
    --light-color: #f0f0f0;
    --dark-color: #0f0f1a;
    --text-color: #ffffff;
    --border-color: rgba(255, 255, 255, 0.1);
    --shadow-color: rgba(0, 0, 0, 0.3);
    
    /* Typography */
    --font-main: 'Poppins', sans-serif;
    --font-alt: 'Montserrat', sans-serif;
    --font-size-large: 2rem;
    --font-size-medium: 1.2rem;
    --font-size-small: 0.9rem;
    
    /* Spacing */
    --spacing-small: 1rem;
    --spacing-medium: 2rem;
    --spacing-large: 4rem;
    
    /* Breakpoints */
    --bp-tablet: 768px;
    --bp-mobile: 480px;
    
    /* Animations */
    --transition-speed: 0.5s;
    --animation-delay: 0.2s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scroll-behavior: smooth;
}

html {
    font-size: 16px;
    scroll-padding-top: 80px;
}

body {
    font-family: var(--font-main);
    font-size: var(--font-size-medium);
    color: var(--text-color);
    background-color: var(--primary-color);
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-x: hidden;
    position: relative;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-alt);
    margin-bottom: 1rem;
    line-height: 1.3;
}

h1 {
    font-size: var(--font-size-large);
}

h2 {
    font-size: 2rem;
}

p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

a:hover {
    color: #f0a500;
}

/* Layout */
.site-header {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 1000;
    background-color: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
}

.navbar-brand .logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--accent-color);
    letter-spacing: 1px;
}

.navbar-brand .logo span {
    color: var(--text-color);
}

.navbar-menu {
    display: flex;
    gap: 2rem;
    list-style: none;
    align-items: center;
}

.navbar-menu .menu-item {
    position: relative;
}

.menu-link {
    position: relative;
    padding: 0.5rem 0;
    display: inline-block;
    transition: color 0.3s ease;
}

.menu-link::after {
    content: '';
    position: absolute;
    width: 0%;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--accent-color);
    transition: width 0.3s ease;
}

.menu-link:hover::after {
    width: 100%;
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 5px;
    width: 30px;
    height: 24px;
    background: none;
    border: none;
    cursor: pointer;
}

.hamburger {
    display: block;
    width: 100%;
    height: 3px;
    background-color: var(--text-color);
    border-radius: 2px;
    position: relative;
    transition: all 0.3s ease;
}

.site-main {
    overflow-x: hidden;
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.parallax-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.parallax-layer {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    will-change: transform;
}

.layer-1 {
    z-index: 1;
}

.layer-2 {
    z-index: 2;
}

.layer-3 {
    z-index: 3;
}

.layer-4 {
    z-index: 4;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.background-shape {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(255,255,255,0.05) 0%, transparent 70%);
    will-change: transform;
}

.shape-1 {
    background: radial-gradient(circle at center, rgba(224,86,253,0.05) 0%, transparent 70%);
}

.shape-2 {
    background: radial-gradient(circle at center, rgba(224,86,253,0.07) 0%, transparent 70%);
}

.shape-3 {
    background: radial-gradient(circle at center, rgba(224,86,253,0.1) 0%, transparent 70%);
}

.hero-content {
    max-width: 800px;
    padding: 2rem;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--text-color);
    position: relative;
    z-index: 5;
}

.hero-title span {
    color: var(--accent-color);
    position: relative;
}

.hero-title span::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--accent-color), #f0a500);
    z-index: -1;
}

.hero-subtitle {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.8);
    position: relative;
    z-index: 5;
}

.btn-primary {
    display: inline-block;
    padding: 0.8rem 2rem;
    background: var(--accent-color);
    color: var(--dark-color);
    font-weight: 600;
    border-radius: 30px;
    transition: all 0.3s ease;
    position: relative;
    z-index: 5;
    box-shadow: 0 5px 15px rgba(224, 86, 253, 0.3);
}

.btn-primary:hover {
    background: #f0a500;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(224, 86, 253, 0.4);
}

/* Portfolio Section */
.portfolio-section {
    padding: 6rem 2rem;
    background-color: var(--secondary-color);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.section-title span {
    color: var(--accent-color);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-color), #f0a500);
}

.section-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.7);
    max-width: 600px;
    margin: 0 auto;
}

.portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.portfolio-item:hover {
    transform: translateY(-5px);
}

.portfolio-image {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.portfolio-image img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.5s ease;
}

.portfolio-item:hover .portfolio-image img {
    transform: scale(1.05);
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(26, 26, 46, 0.7) 0%, rgba(26, 26, 46, 0.9) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1.5rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.portfolio-item:hover .portfolio-info {
    transform: translateY(0);
}

.portfolio-info h3 {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
}

.portfolio-info p {
    font-size: 0.9rem;
    color: var(--accent-color);
}

/* Testimonials Section */
.testimonials-section {
    padding: 6rem 2rem;
    background-color: var(--primary-color);
}

.testimonials-carousel {
    display: flex;
    overflow-x: auto;
    gap: 2rem;
    padding: 2rem;
    scroll-snap-type: x mandatory;
    scrollbar-width: none;
}

.testimonials-carousel::-webkit-scrollbar {
    display: none;
}

.testimonial-item {
    flex: 0 0 80%;
    max-width: 600px;
    padding: 2rem;
    background: var(--secondary-color);
    border-radius: 15px;
    position: relative;
    scroll-snap-align: start;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.testimonial-item::before {
    content: '“';
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 4rem;
    color: var(--accent-color);
    opacity: 0.2;
}

.testimonial-content {
    position: relative;
    z-index: 1;
    margin-bottom: 2rem;
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.8;
    position: relative;
    z-index: 1;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.testimonial-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border: 2px solid var(--accent-color);
}

.testimonial-info h4 {
    font-size: 1rem;
    margin-bottom: 0.3rem;
    color: var(--text-color);
}

.testimonial-info p {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

/* Contact Section */
.contact-section {
    padding: 6rem 2rem;
    background-color: var(--secondary-color);
}

.contact-container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    background: var(--primary-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item svg {
    width: 24px;
    height: 24px;
    fill: var(--accent-color);
}

.contact-item span {
    color: var(--text-color);
}

.contact-form {
    background: var(--primary-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: none;
    border-radius: 8px;
    background: var(--secondary-color);
    color: var(--text-color);
    font-size: 1rem;
    resize: vertical;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.form-group textarea {
    min-height: 120px;
}

.form-error {
    color: #ff4d4d;
    font-size: 0.85rem;
    margin-top: 0.3rem;
    display: none;
}

.form-group.error .form-error {
    display: block;
}

/* Footer */
.site-footer {
    background-color: var(--dark-color);
    padding: 4rem 2rem;
    color: rgba(255, 255, 255, 0.7);
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1.5fr 1fr 1fr;
    gap: 3rem;
    padding-bottom: 3rem;
    border-bottom: 1px solid var(--border-color);
}

.footer-brand .logo {
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.footer-brand p {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    color: rgba(255, 255, 255, 0.7);
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icons a {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: var(--secondary-color);
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background: var(--accent-color);
}
    background: var(--accent-color);
